import backtrader as bt
from datetime import datetime, timedelta
from influxdb_feed import InfluxDBData

# InfluxDB 配置
INFLUXDB_URL = "http://localhost:8086"
INFLUXDB_TOKEN = "hVYowbpzTMKLUjkwQK-zYNEz-Y7MsqlU3R61xutcm1t3as0vcNzIH4a7nkYi2y27N0Iw9Nt6cf1wrBd2-1WmtA=="
INFLUXDB_ORG = "zhongyixin"
BUCKET_NAME = "stock_market_data"

class TestStrategy(bt.Strategy):
    def __init__(self):
        self.dataclose = self.datas[0].close
        self.order = None
        self.count = 0
        
    def next(self):
        # 简单地打印每个数据点
        self.count += 1
        if self.count <= 5:  # 只打印前5个数据点
            print(f'日期: {self.datas[0].datetime.date(0)}')
            print(f'开盘: {self.datas[0].open[0]:.2f}')
            print(f'最高: {self.datas[0].high[0]:.2f}')
            print(f'最低: {self.datas[0].low[0]:.2f}')
            print(f'收盘: {self.datas[0].close[0]:.2f}')
            print(f'成交量: {self.datas[0].volume[0]:.0f}')
            print('-------------------')

def test_influxdb_feed():
    # 创建 Cerebro 引擎
    cerebro = bt.Cerebro()
    
    # 设置初始资金
    cerebro.broker.setcash(100000.0)
    
    # 添加策略
    cerebro.addstrategy(TestStrategy)
    
    # 创建数据源
    end_date = datetime(2025, 2, 21)  # 根据之前测试看到的数据日期
    start_date = end_date - timedelta(days=30)  # 获取30天的数据
    
    data = InfluxDBData(
        dataname='000001',  # 测试平安银行的数据
        host='localhost',
        port=8086,
        token=INFLUXDB_TOKEN,
        org=INFLUXDB_ORG,
        bucket=BUCKET_NAME,
        measurement='market_data',
        fromdate=start_date,
        todate=end_date
    )
    
    # 添加数据到回测引擎
    cerebro.adddata(data)
    
    # 打印初始资金
    print('初始资金: %.2f' % cerebro.broker.getvalue())
    
    try:
        # 运行回测
        print('\n开始回测...')
        cerebro.run()
        print('回测完成!')
        
        # 打印最终资金
        print('最终资金: %.2f' % cerebro.broker.getvalue())
        
    except Exception as e:
        print(f'回测过程中发生错误: {e}')

if __name__ == '__main__':
    test_influxdb_feed() 
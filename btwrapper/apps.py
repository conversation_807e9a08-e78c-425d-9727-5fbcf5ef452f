from django.apps import AppConfig
from django.db import connection
from django.db.utils import OperationalError
import atexit
import threading
import time


class BtwrapperConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "btwrapper"

    def ready(self):
        # 避免在自动重载时多次执行
        import os

        if os.environ.get("RUN_MAIN") != "true":
            # 导入任务管理器
            from .backtest_tasks import shutdown
            
            # 延迟恢复任务，避免在应用初始化时访问数据库
            self._schedule_task_recovery()

            # 注册应用关闭时的清理函数
            atexit.register(shutdown)
    
    def _schedule_task_recovery(self):
        """延迟执行任务恢复，避免在应用初始化时访问数据库"""
        def delayed_recovery():
            # 等待一段时间确保应用完全初始化
            time.sleep(2)
            
            # 检查数据库连接是否可用
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    # 测试数据库连接
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                    
                    # 数据库可用，恢复任务
                    from .backtest_tasks import recover_pending_tasks
                    recover_pending_tasks()
                    print("成功恢复未完成的回测任务")
                    break
                    
                except OperationalError as e:
                    if attempt < max_retries - 1:
                        print(f"数据库连接失败，{2}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(2)
                    else:
                        print(f"无法连接到数据库，跳过任务恢复: {e}")
                except Exception as e:
                    print(f"恢复任务时出错: {e}")
                    break
        
        # 在后台线程中执行延迟恢复
        recovery_thread = threading.Thread(target=delayed_recovery, daemon=True)
        recovery_thread.start()

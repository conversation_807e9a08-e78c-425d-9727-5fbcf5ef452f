# Generated by Django 5.2 on 2025-05-15 14:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("btwrapper", "0002_backtesttask"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="backtesttask",
            name="broker_config",
            field=models.JSONField(blank=True, help_text="Broker配置", null=True),
        ),
        migrations.AddField(
            model_name="backtesttask",
            name="cerebro_config",
            field=models.JSONField(blank=True, help_text="Cerebro配置", null=True),
        ),
        migrations.AlterField(
            model_name="backtesttask",
            name="user",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]

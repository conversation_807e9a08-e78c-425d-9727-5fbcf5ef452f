# Generated by Django 5.2 on 2025-05-15 15:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("btwrapper", "0004_backtestlog_portfoliovalue_traderecord"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="backtesttask",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "回测任务",
                "verbose_name_plural": "回测任务",
            },
        ),
        migrations.AlterField(
            model_name="backtesttask",
            name="parameters",
            field=models.JSONField(default=dict, help_text="策略参数"),
        ),
        migrations.AlterField(
            model_name="backtesttask",
            name="user",
            field=models.ForeignKey(
                blank=True,
                help_text="任务创建者，允许匿名用户",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="backtest_tasks",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]

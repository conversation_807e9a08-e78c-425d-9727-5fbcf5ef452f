# Generated by Django 5.1.3 on 2025-05-13 09:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("btwrapper", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BacktestTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("strategy_code", models.TextField(help_text="策略代码")),
                (
                    "strategy_name",
                    models.CharField(help_text="策略名称", max_length=100),
                ),
                ("parameters", models.JSONField(help_text="策略参数")),
                ("symbols", models.J<PERSON><PERSON>ield(help_text="交易标的列表")),
                ("start_date", models.DateTimeField(help_text="回测开始时间")),
                ("end_date", models.DateTimeField(help_text="回测结束时间")),
                ("timeframe", models.CharField(help_text="数据周期", max_length=20)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("running", "运行中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("result", models.JSONField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]

# Generated by Django 5.2 on 2025-05-15 15:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("btwrapper", "0003_backtesttask_broker_config_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BacktestLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "信息"),
                            ("warning", "警告"),
                            ("error", "错误"),
                            ("trade", "交易"),
                        ],
                        max_length=10,
                    ),
                ),
                ("message", models.TextField()),
                ("timestamp", models.DateTimeField(help_text="日志时间")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="btwrapper.backtesttask",
                    ),
                ),
            ],
            options={
                "ordering": ["timestamp"],
            },
        ),
        migrations.CreateModel(
            name="PortfolioValue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(help_text="时间点")),
                ("cash", models.FloatField(help_text="现金")),
                ("total_value", models.FloatField(help_text="总资产")),
                ("market_value", models.FloatField(help_text="持仓市值")),
                ("pnl", models.FloatField(help_text="累计盈亏")),
                ("returns", models.FloatField(help_text="收益率")),
                ("drawdown", models.FloatField(help_text="回撤", null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="portfolio_values",
                        to="btwrapper.backtesttask",
                    ),
                ),
            ],
            options={
                "ordering": ["timestamp"],
            },
        ),
        migrations.CreateModel(
            name="TradeRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(help_text="交易标的", max_length=50)),
                (
                    "trade_type",
                    models.CharField(
                        choices=[("buy", "买入"), ("sell", "卖出")], max_length=10
                    ),
                ),
                ("price", models.FloatField(help_text="成交价格")),
                ("size", models.FloatField(help_text="成交数量")),
                ("commission", models.FloatField(help_text="手续费")),
                ("trade_time", models.DateTimeField(help_text="成交时间")),
                ("pnl", models.FloatField(help_text="交易盈亏", null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="trades",
                        to="btwrapper.backtesttask",
                    ),
                ),
            ],
            options={
                "ordering": ["trade_time"],
            },
        ),
    ]

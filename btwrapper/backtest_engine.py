import backtrader as bt
import importlib.util
import sys
from typing import Dict, Any
from datetime import datetime
from django.utils import timezone
from btwrapper.models import BacktestTask, TradeRecord, PortfolioValue, BacktestLog
from btwrapper.influxdb_feed import InfluxDBData


class BacktestEngine:
    def __init__(self, task: BacktestTask):
        self.task = task
        # InfluxDB 配置
        self.influxdb_config = {
            'host': 'localhost',
            'port': 8086,
            'token': 'hVYowbpzTMKLUjkwQK-zYNEz-Y7MsqlU3R61xutcm1t3as0vcNzIH4a7nkYi2y27N0Iw9Nt6cf1wrBd2-1WmtA==',
            'org': 'zhongyixin',
            'bucket': 'stock_market_data',
            'measurement': 'market_data'
        }
        self.cerebro = None
    
    def log(self, message: str, level: str = 'info'):
        """记录日志"""
        BacktestLog.objects.create(
            task=self.task,
            level=level,
            message=message,
            timestamp=timezone.now()
        )

    def record_trade(self, trade_type: str, price: float, size: float, commission: float, 
                    trade_time: datetime, symbol: str, pnl: float = None):
        """记录交易"""
        TradeRecord.objects.create(
            task=self.task,
            trade_type=trade_type,
            price=price,
            size=size,
            commission=commission,
            trade_time=trade_time,
            symbol=symbol,
            pnl=pnl
        )

    def record_portfolio_value(self, timestamp: datetime, cash: float, total_value: float,
                             market_value: float, pnl: float, returns: float, drawdown: float = None):
        """记录资金曲线"""
        PortfolioValue.objects.create(
            task=self.task,
            timestamp=timestamp,
            cash=cash,
            total_value=total_value,
            market_value=market_value,
            pnl=pnl,
            returns=returns,
            drawdown=drawdown
        )

    def create_observers(self, cerebro: bt.Cerebro):
        """创建观察者，用于记录回测过程中的数据"""
        class PortfolioObserver(bt.Observer):
            lines = ('total_value', 'market_value', 'cash', 'drawdown')
            plotinfo = dict(plot=True, subplot=True)

            def next(self):
                self.lines.total_value[0] = self._owner.broker.getvalue()
                self.lines.market_value[0] = self._owner.broker.getvalue() - self._owner.broker.getcash()
                self.lines.cash[0] = self._owner.broker.getcash()
                # 计算回撤
                if len(self.lines.total_value) > 1:
                    peak = max([self.lines.total_value[-i] for i in range(len(self.lines.total_value))])
                    self.lines.drawdown[0] = (peak - self.lines.total_value[0]) / peak if peak > 0 else 0

        cerebro.addobserver(PortfolioObserver)
    
    def load_strategy_class(self, strategy_code: str, strategy_name: str) -> type:
        """动态加载策略类"""
        try:
            # 创建模块规范
            spec = importlib.util.spec_from_loader(
                strategy_name, 
                loader=None, 
                origin=strategy_name
            )
            
            # 创建新模块
            module = importlib.util.module_from_spec(spec)
            
            # 注入必要的依赖
            module.bt = bt  # 注入 backtrader
            
            # 将一些常用的 backtrader 类直接暴露给用户代码
            for name in ['Indicator', 'Strategy', 'ind']:
                setattr(module, name, getattr(bt, name))
            
            # 注入常用的 indicators
            for name in dir(bt.indicators):
                if not name.startswith('_'):  # 只注入公开的 indicators
                    setattr(module, name, getattr(bt.indicators, name))
            
            # 将模块添加到sys.modules
            sys.modules[strategy_name] = module
            
            # 执行策略代码
            exec(strategy_code, module.__dict__)
            
            # 获取所有自定义的类
            custom_classes = {
                name: obj for name, obj in module.__dict__.items()
                if isinstance(obj, type) and not name.startswith('_')
            }
            
            # 获取策略类
            strategy_class = None
            for name, cls in custom_classes.items():
                if issubclass(cls, bt.Strategy) and cls != bt.Strategy:
                    strategy_class = cls
                    break
            
            if not strategy_class:
                raise ValueError("未找到策略类")
            
            return strategy_class
            
        except Exception as e:
            raise ValueError(f"策略代码加载失败: {str(e)}")
    
    def create_sandbox_globals(self):
        """创建安全的全局命名空间"""
        safe_builtins = {
            'True': True,
            'False': False,
            'None': None,
            'abs': abs,
            'float': float,
            'int': int,
            'len': len,
            'max': max,
            'min': min,
            'round': round,
            'sum': sum,
            'range': range,
            # 可以添加其他安全的内置函数
        }
        return safe_builtins
    
    def run_backtest(self) -> Dict[str, Any]:
        """执行回测"""
        try:
            # 更新任务状态
            self.task.status = 'running'
            self.task.save()

            # 创建cerebro实例并应用配置
            self.cerebro = bt.Cerebro()
            if self.task.cerebro_config:
                self.apply_cerebro_config(self.cerebro, self.task.cerebro_config)
            
            # 加载策略类
            strategy_class = self.load_strategy_class(
                self.task.strategy_code,
                self.task.strategy_name
            )
            
            # 添加策略，并传入 engine 实例
            self.cerebro.addstrategy(strategy_class, engine=self, **self.task.parameters)
            
            # 加载数据
            for symbol in self.task.symbols:
                if symbol.endswith('.csv'):
                    # 如果是 CSV 文件，使用 GenericCSVData
                    data = bt.feeds.GenericCSVData(
                        dataname=symbol,
                        fromdate=self.task.start_date,
                        todate=self.task.end_date,
                        nullvalue=0.0,
                        dtformat='%Y-%m-%d',
                        datetime=0,
                        open=1,
                        high=2,
                        low=3,
                        close=4,
                        volume=5,
                        openinterest=-1
                    )
                else:
                    # 使用 InfluxDB 数据源
                    data = InfluxDBData(
                        dataname=symbol,
                        host=self.influxdb_config['host'],
                        port=self.influxdb_config['port'],
                        token=self.influxdb_config['token'],
                        org=self.influxdb_config['org'],
                        bucket=self.influxdb_config['bucket'],
                        measurement=self.influxdb_config['measurement'],
                        fromdate=self.task.start_date,
                        todate=self.task.end_date
                    )
                self.cerebro.adddata(data, name=symbol)
            
            # 应用Broker配置
            if self.task.broker_config:
                self.apply_broker_config(self.cerebro.broker, self.task.broker_config)
            
            # 添加分析器
            self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
            self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
            self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
            
            # 添加观察者
            self.create_observers(self.cerebro)
            
            # 运行回测
            results = self.cerebro.run()
            strategy = results[0]
            
            # 获取回测结果
            portfolio_stats = self.get_portfolio_stats(strategy)
            
            # 更新任务状态和结果
            self.task.status = 'completed'
            self.task.result = portfolio_stats
            self.task.save()
            
            return portfolio_stats
            
        except Exception as e:
            error_msg = f"回测执行失败: {str(e)}"
            self.log(error_msg, level='error')
            self.task.status = 'failed'
            self.task.error_message = error_msg
            self.task.save()
            raise RuntimeError(error_msg)
    
    def apply_cerebro_config(self, cerebro, config: Dict[str, Any]):
        """应用用户提供的Cerebro配置"""
        valid_params = [
            'preload', 'runonce', 'maxcpus', 'stdstats', 'oldbuysell', 
            'oldtrades', 'lookahead', 'exactbars', 'optdatas', 'optreturn',
            'objcache', 'live', 'writer', 'tradehistory', 'oldsync',
            'tz', 'cheat_on_open', 'broker_coo', 'quicknotify'
        ]
        
        for param, value in config.items():
            if param in valid_params:
                setattr(cerebro.params, param, value)
            
        # 特殊处理时区
        if 'tz' in config and config['tz']:
            try:
                import pytz
                cerebro.addtz(pytz.timezone(config['tz']))
            except Exception:
                pass
            
        return cerebro
    
    def apply_broker_config(self, broker, config: Dict[str, Any]):
        """应用用户提供的Broker配置"""
        # 设置初始资金
        if 'cash' in config:
            broker.set_cash(config['cash'])
        
        # 设置滑点
        slip_perc = config.get('slip_perc', 0.0)
        slip_fixed = config.get('slip_fixed', 0.0)
        
        if slip_perc > 0:
            broker.set_slippage_perc(
                slip_perc,
                slip_open=config.get('slip_open', False),
                slip_limit=config.get('slip_limit', True),
                slip_match=config.get('slip_match', True),
                slip_out=config.get('slip_out', False)
            )
        elif slip_fixed > 0:
            broker.set_slippage_fixed(
                slip_fixed,
                slip_open=config.get('slip_open', False),
                slip_limit=config.get('slip_limit', True),
                slip_match=config.get('slip_match', True),
                slip_out=config.get('slip_out', False)
            )
        
        # 设置各种 broker 参数
        broker_params = [
            ('checksubmit', 'set_checksubmit'),
            ('eosbar', 'set_eosbar'),
            ('coc', 'set_coc'),
            ('coo', 'set_coo'),
            ('int2pnl', 'set_int2pnl'),
            ('shortcash', 'set_shortcash'),
            ('fundstartval', 'set_fundstartval'),
            ('fundmode', 'set_fundmode'),
        ]
        
        for param, method in broker_params:
            if param in config:
                getattr(broker, method)(config[param])
            
        return broker
    
    def get_portfolio_stats(self, strategy) -> Dict[str, Any]:
        """获取回测结果统计"""
        # 获取分析器结果
        sharpe = strategy.analyzers.sharpe.get_analysis()
        drawdown = strategy.analyzers.drawdown.get_analysis()
        trades = strategy.analyzers.trades.get_analysis()
        
        # 获取交易统计
        total_trades = trades.get('total', {}).get('total', 0)
        total_won = trades.get('won', {}).get('total', 0)
        total_lost = trades.get('lost', {}).get('total', 0)
        
        return {
            'summary': {
                'initial_value': strategy.broker.startingcash,
                'final_value': strategy.broker.getvalue(),
                'total_return': strategy.broker.getvalue() / strategy.broker.startingcash - 1,
                'sharpe_ratio': sharpe.get('sharperatio', 0.0),
                'max_drawdown': drawdown.get('max', {}).get('drawdown', 0.0),
                'max_drawdown_length': drawdown.get('max', {}).get('len', 0),
            },
            'trade_analysis': {
                'total_trades': total_trades,
                'total_won': total_won,
                'total_lost': total_lost,
                'win_rate': total_won / max(total_trades, 1),
                'avg_trade': trades.get('pnl', {}).get('average', 0.0),
                'avg_win': trades.get('won', {}).get('pnl', {}).get('average', 0.0),
                'avg_loss': trades.get('lost', {}).get('pnl', {}).get('average', 0.0),
            }
        } 
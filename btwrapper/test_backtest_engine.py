from datetime import datetime
from django.test import TestCase
from django.utils import timezone
import pytz
from .backtest_engine import BacktestEngine
from .models import BacktestTask, TradeRecord, PortfolioValue, BacktestLog
import backtrader as bt
import pandas as pd
import numpy as np

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start='2024-05-16', end='2025-04-17', freq='D')
    prices = np.random.normal(2000, 100, size=len(dates))
    data = pd.DataFrame({
        'open': prices,
        'high': prices * 1.02,
        'low': prices * 0.98,
        'close': prices * 1.01,
        'volume': np.random.randint(100000, 1000000, size=len(dates)),
    }, index=dates)
    return data

class TestBacktestEngine(TestCase):
    def setUp(self):
        self.test_data = create_test_data()
        self.test_data.to_csv('test_data.csv')
        
    def tearDown(self):
        import os
        if os.path.exists('test_data.csv'):
            os.remove('test_data.csv')
            
    def test_backtest_engine(self):
        # 创建一个简单的移动平均策略代码
        strategy_code = '''
import backtrader as bt
from django.utils import timezone
import pytz

class SimpleMAStrategy(bt.Strategy):
    params = dict(
        ma_period=20,
        printlog=False,
        engine=None,  # 添加 engine 参数
    )

    def log(self, txt, dt=None, doprint=False):
        if self.params.printlog or doprint:
            dt = dt or self.datas[0].datetime.date(0)
            self.params.engine.log(f'{dt.isoformat()} {txt}', level='info')

    def __init__(self):
        self.dataclose = self.datas[0].close
        self.order = None
        self.buyprice = None
        self.buycomm = None
        self.sma = bt.indicators.SimpleMovingAverage(
            self.datas[0], period=self.params.ma_period)

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return

        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'买入执行: 价格={order.executed.price:.2f}, '
                    f'成本={order.executed.value:.2f}, '
                    f'手续费={order.executed.comm:.2f}'
                )
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                # 获取当前时间并添加时区信息
                trade_time = self.datas[0].datetime.datetime(0)
                if timezone.is_naive(trade_time):
                    trade_time = timezone.make_aware(trade_time, pytz.UTC)
                # 记录买入交易
                self.params.engine.record_trade(
                    trade_type='buy',
                    price=order.executed.price,
                    size=order.executed.size,
                    commission=order.executed.comm,
                    trade_time=trade_time,
                    symbol=self.datas[0]._name
                )
            else:
                self.log(
                    f'卖出执行: 价格={order.executed.price:.2f}, '
                    f'成本={order.executed.value:.2f}, '
                    f'手续费={order.executed.comm:.2f}'
                )
                # 获取当前时间并添加时区信息
                trade_time = self.datas[0].datetime.datetime(0)
                if timezone.is_naive(trade_time):
                    trade_time = timezone.make_aware(trade_time, pytz.UTC)
                # 记录卖出交易
                self.params.engine.record_trade(
                    trade_type='sell',
                    price=order.executed.price,
                    size=order.executed.size,
                    commission=order.executed.comm,
                    trade_time=trade_time,
                    symbol=self.datas[0]._name
                )

            self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('订单取消/保证金不足/拒绝')

        self.order = None

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        self.log(f'交易利润, 毛利润={trade.pnl:.2f}, 净利润={trade.pnlcomm:.2f}')

    def next(self):
        # 获取当前时间并添加时区信息
        current_time = self.datas[0].datetime.datetime(0)
        if timezone.is_naive(current_time):
            current_time = timezone.make_aware(current_time, pytz.UTC)
            
        # 记录当前的资金曲线
        self.params.engine.record_portfolio_value(
            timestamp=current_time,
            cash=self.broker.getcash(),
            total_value=self.broker.getvalue(),
            market_value=self.broker.getvalue() - self.broker.getcash(),
            pnl=self.broker.getvalue() - self.broker.startingcash,
            returns=(self.broker.getvalue() / self.broker.startingcash - 1),
            drawdown=None  # 在 Observer 中计算
        )

        self.log(f'收盘价, {self.dataclose[0]:.2f}')

        if self.order:
            return

        if not self.position:
            if self.dataclose[0] > self.sma[0]:
                self.log(f'买入信号, {self.dataclose[0]:.2f}')
                self.order = self.buy()
        else:
            if self.dataclose[0] < self.sma[0]:
                self.log(f'卖出信号, {self.dataclose[0]:.2f}')
                self.order = self.sell()

    def stop(self):
        self.log('结束交易', doprint=True)
'''

        # 创建回测任务
        task = BacktestTask.objects.create(
            strategy_code=strategy_code,
            strategy_name='SimpleMAStrategy',
            symbols=['test_data.csv'],  # 使用测试数据文件
            start_date=timezone.make_aware(datetime(2024, 5, 1), pytz.UTC),
            end_date=timezone.make_aware(datetime(2025, 4, 17), pytz.UTC),
            timeframe='daily',
            parameters={'ma_period': 20, 'printlog': True},
            cerebro_config={
                'stdstats': True,
                'oldbuysell': False,
            },
            broker_config={
                'cash': 100000.0,
                'slip_perc': 0.001,  # 0.1% 滑点
            }
        )

        try:
            # 创建回测引擎
            engine = BacktestEngine(task)
            
            print("开始回测...")
            print(f"策略: SimpleMAStrategy")
            print(f"股票: {task.symbols}")
            print(f"时间范围: {task.start_date.date()} 至 {task.end_date.date()}")
            print(f"初始资金: {task.broker_config['cash']:.2f}")
            print("-------------------")
            
            # 运行回测
            results = engine.run_backtest()
            
            # 验证回测结果
            self.assertIsNotNone(results)
            self.assertIn('summary', results)
            self.assertIn('trade_analysis', results)
            
            # 验证是否生成了交易记录
            trades = TradeRecord.objects.filter(task=task)
            self.assertTrue(trades.exists())
            
            # 验证是否生成了资金曲线
            portfolio_values = PortfolioValue.objects.filter(task=task)
            self.assertTrue(portfolio_values.exists())
            
            # 验证是否生成了日志
            logs = BacktestLog.objects.filter(task=task)
            self.assertTrue(logs.exists())
            
            # 打印回测结果
            print("\n回测结果:")
            print(f"最终资金: {results['summary']['final_value']:.2f}")
            print(f"总收益率: {results['summary']['total_return']*100:.2f}%")
            print(f"夏普比率: {results['summary']['sharpe_ratio']:.2f}")
            print(f"最大回撤: {results['summary']['max_drawdown']*100:.2f}%")
            
            print("\n交易统计:")
            print(f"总交易次数: {results['trade_analysis']['total_trades']}")
            print(f"盈利交易: {results['trade_analysis']['total_won']}")
            print(f"亏损交易: {results['trade_analysis']['total_lost']}")
            print(f"胜率: {results['trade_analysis']['win_rate']*100:.2f}%")
            print(f"平均收益: {results['trade_analysis']['avg_trade']:.2f}")
            
        except Exception as e:
            self.fail(f"回测过程中发生错误: {e}") 
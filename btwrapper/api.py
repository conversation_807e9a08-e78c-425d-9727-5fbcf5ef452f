from django.shortcuts import get_object_or_404
from ninja import Schema, Router
from ninja_extra import api_controller, http_get, http_post, route
from typing import List, Optional
from datetime import date
from .models import BacktestTask, TradeRecord, PortfolioValue
from .backtest_tasks import submit_backtest, get_task_status


# 定义请求和响应的数据模型
class CerebroConfig(Schema):
    preload: bool = True
    runonce: bool = True
    stdstats: bool = True
    oldbuysell: bool = False
    oldtrades: bool = False
    optdatas: bool = True
    optreturn: bool = True
    objcache: bool = False
    exactbars: bool = False
    writer: bool = False
    tradehistory: bool = False
    oldsync: bool = False
    cheat_on_open: bool = False
    broker_coo: bool = True
    quicknotify: bool = False
    tz: Optional[str] = None
    maxcpus: Optional[int] = None
    lookahead: int = 0
    slip_out: bool = False
    fundmode: bool = False
    checksubmit: bool = True
    eosbar: bool = False
    coc: bool = False
    coo: bool = False
    int2pnl: bool = True
    shortcash: bool = True


class BrokerConfig(Schema):
    cash: float = 10000.0
    fundstartval: float = 100.0
    slip_perc: float = 0.0
    slip_fixed: float = 0.0
    slip_open: bool = False
    slip_match: bool = True
    slip_limit: bool = True
    slip_out: bool = False
    fundmode: bool = False
    checksubmit: bool = True
    eosbar: bool = False
    coc: bool = False
    coo: bool = False
    int2pnl: bool = True
    shortcash: bool = True


class BacktestRequest(Schema):
    strategy_code: str
    strategy_name: str
    symbols: List[str]
    start_date: date
    end_date: date
    timeframe: str
    cerebro_config: CerebroConfig
    broker_config: BrokerConfig


class BacktestResponse(Schema):
    status: str
    message: str
    task_id: str


@api_controller("/backtest", tags=["Backtest"])
class BacktestController:
    @http_post("/", response=BacktestResponse)
    def create_backtest(self, request, payload: BacktestRequest):
        """
        创建回测任务
        """
        # 创建任务记录
        task = BacktestTask.objects.create(
            user=request.user if request.user.is_authenticated else None,  # 修改这里，只在用户已认证时设置user
            strategy_name=payload.strategy_name,
            strategy_code=payload.strategy_code,
            symbols=payload.symbols,
            start_date=payload.start_date,
            end_date=payload.end_date,
            timeframe=payload.timeframe,
            cerebro_config=payload.cerebro_config.dict(),
            broker_config=payload.broker_config.dict(),
            status="pending",
        )

        # 启动线程执行任务
        submit_backtest(task.id)

        return {"status": "success", "message": "回测任务已创建", "task_id": str(task.id)}

    @http_get("/{task_id}")
    def get_backtest_task(self, request, task_id: int):
        """获取回测任务状态和结果"""
        task = get_object_or_404(BacktestTask, id=task_id, user=request.user)

        # 获取线程管理器中的任务运行状态
        runtime_info = get_task_status(task_id)

        return {
            "id": task.id,
            "status": task.status,
            "is_running": runtime_info["is_running"],
            "runtime": (
                runtime_info.get("runtime", 0) if runtime_info["is_running"] else None
            ),
            "created_at": task.created_at,
            "result": task.result if task.status == "completed" else None,
            "error": task.error_message if task.status == "failed" else None,
        }

    @http_get("/{task_id}/status")
    def get_backtest_status(self, request, task_id: str):
        """获取回测任务状态"""
        task = get_object_or_404(BacktestTask, id=task_id)
        return {
            "status": task.status,
            "error_message": task.error_message if task.status == "failed" else None
        }

    @http_get("/{task_id}/results")
    def get_backtest_results(self, request, task_id: str):
        """获取回测结果详情"""
        task = get_object_or_404(BacktestTask, id=task_id)
        
        # 获取交易记录
        trades = TradeRecord.objects.filter(task=task).order_by('trade_time')
        trade_list = [{
            'trade_time': trade.trade_time.strftime('%Y-%m-%d %H:%M:%S'),
            'trade_type': trade.trade_type,
            'symbol': trade.symbol,
            'price': trade.price,
            'size': trade.size,
            'commission': trade.commission,
            'pnl': trade.pnl
        } for trade in trades]
        
        # 获取资金曲线数据
        portfolio_values = PortfolioValue.objects.filter(task=task).order_by('timestamp')
        portfolio_data = [{
            'timestamp': value.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'total_value': value.total_value,
            'market_value': value.market_value,
            'cash': value.cash,
            'pnl': value.pnl,
            'returns': value.returns,
            'drawdown': value.drawdown
        } for value in portfolio_values]
        
        return {
            "summary": task.result["summary"] if task.result else None,
            "trade_analysis": task.result["trade_analysis"] if task.result else None,
            "trades": trade_list,
            "portfolio_values": portfolio_data
        }

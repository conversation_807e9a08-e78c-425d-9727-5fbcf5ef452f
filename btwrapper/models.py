from django.db import models
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any, Optional

User = get_user_model()


# Create your models here.
class Strategy(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    code = models.TextField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class BacktestTask(models.Model):
    STATUS_CHOICES = [
        ("pending", "等待中"),
        ("running", "运行中"),
        ("completed", "已完成"),
        ("failed", "失败"),
    ]

    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL,  # 用户删除时不删除任务
        null=True,  # 允许匿名用户创建任务
        blank=True,  # 允许在表单中留空
        related_name='backtest_tasks',
        help_text="任务创建者，允许匿名用户"
    )
    strategy_code = models.TextField(help_text="策略代码")
    strategy_name = models.CharField(max_length=100, help_text="策略名称")
    parameters = models.JSONField(help_text="策略参数", default=dict)
    cerebro_config = models.JSONField(help_text="Cerebro配置", null=True, blank=True)
    broker_config = models.JSONField(help_text="Broker配置", null=True, blank=True)

    # 回测配置
    symbols = models.JSONField(help_text="交易标的列表")
    start_date = models.DateTimeField(help_text="回测开始时间")
    end_date = models.DateTimeField(help_text="回测结束时间")
    timeframe = models.CharField(max_length=20, help_text="数据周期")

    # 任务状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    error_message = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # 回测结果
    result = models.JSONField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = '回测任务'
        verbose_name_plural = '回测任务'

    def __str__(self):
        return f"{self.strategy_name} - {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"


class TradeRecord(models.Model):
    """交易记录"""

    TRADE_TYPE_CHOICES = [("buy", "买入"), ("sell", "卖出")]

    task = models.ForeignKey(
        BacktestTask, on_delete=models.CASCADE, related_name="trades"
    )
    symbol = models.CharField(max_length=50, help_text="交易标的")
    trade_type = models.CharField(max_length=10, choices=TRADE_TYPE_CHOICES)
    price = models.FloatField(help_text="成交价格")
    size = models.FloatField(help_text="成交数量")
    commission = models.FloatField(help_text="手续费")
    trade_time = models.DateTimeField(help_text="成交时间")
    pnl = models.FloatField(help_text="交易盈亏", null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["trade_time"]


class PortfolioValue(models.Model):
    """资金曲线"""

    task = models.ForeignKey(
        BacktestTask, on_delete=models.CASCADE, related_name="portfolio_values"
    )
    timestamp = models.DateTimeField(help_text="时间点")
    cash = models.FloatField(help_text="现金")
    total_value = models.FloatField(help_text="总资产")
    market_value = models.FloatField(help_text="持仓市值")
    pnl = models.FloatField(help_text="累计盈亏")
    returns = models.FloatField(help_text="收益率")
    drawdown = models.FloatField(help_text="回撤", null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["timestamp"]


class BacktestLog(models.Model):
    """回测日志"""

    LOG_LEVEL_CHOICES = [
        ("info", "信息"),
        ("warning", "警告"),
        ("error", "错误"),
        ("trade", "交易"),
    ]

    task = models.ForeignKey(
        BacktestTask, on_delete=models.CASCADE, related_name="logs"
    )
    level = models.CharField(max_length=10, choices=LOG_LEVEL_CHOICES)
    message = models.TextField()
    timestamp = models.DateTimeField(help_text="日志时间")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["timestamp"]

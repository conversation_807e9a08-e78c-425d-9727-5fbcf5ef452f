"""
回测任务管理器 - 使用进程池执行回测任务

本模块负责:
1. 管理回测任务的执行
2. 使用进程池隔离任务执行环境
3. 处理任务状态更新和结果保存
"""

import os
import time
import signal
import logging
import traceback
from concurrent.futures import ProcessPoolExecutor, TimeoutError
from django.db import connections
from django.conf import settings

from .models import BacktestTask

logger = logging.getLogger(__name__)

# 进程池配置
MAX_WORKERS = getattr(settings, 'BACKTEST_MAX_WORKERS', os.cpu_count())
TASK_TIMEOUT = getattr(settings, 'BACKTEST_TASK_TIMEOUT', 3600)  # 默认1小时超时
executor = ProcessPoolExecutor(max_workers=MAX_WORKERS)

# 任务状态跟踪
active_tasks = {}

def handle_timeout(signum, frame):
    """超时信号处理器"""
    raise TimeoutError("任务执行超时")

def run_backtest_worker(task_id):
    """
    工作进程中执行回测的函数
    
    这个函数会在独立的进程中运行，负责:
    1. 读取任务配置
    2. 初始化和运行回测引擎
    3. 保存结果并更新任务状态
    
    Args:
        task_id: 回测任务的ID
    """
    # 设置任务超时处理
    signal.signal(signal.SIGALRM, handle_timeout)
    signal.alarm(TASK_TIMEOUT)
    
    # 导入需要在子进程中运行的模块
    from .backtest_engine import BacktestEngine
    
    logger.info(f"开始执行回测任务 #{task_id}")
    start_time = time.time()
    
    try:
        # 获取任务信息
        task = BacktestTask.objects.get(id=task_id)
        
        # 更新任务状态为运行中
        task.status = 'running'
        task.save()
        
        # 初始化回测引擎
        engine = BacktestEngine(task)
        
        # 运行回测
        results = engine.run_backtest()
        
        # 回测完成，保存结果
        task.refresh_from_db()  # 重新从数据库获取以避免覆盖其他更新
        task.status = 'completed'
        task.result = results
        task.save()
        
        duration = time.time() - start_time
        logger.info(f"回测任务 #{task_id} 成功完成，耗时: {duration:.2f}秒")
        
        return {
            'success': True,
            'task_id': task_id,
            'duration': duration
        }
        
    except TimeoutError:
        error_msg = f"回测任务 #{task_id} 执行超时（超过 {TASK_TIMEOUT} 秒）"
        logger.error(error_msg)
        
        try:
            task = BacktestTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = error_msg
            task.save()
        except Exception as db_error:
            logger.error(f"无法更新超时任务状态: {str(db_error)}")
            
        return {
            'success': False,
            'task_id': task_id,
            'error': error_msg
        }
        
    except Exception as e:
        # 记录异常并更新任务状态
        error_detail = traceback.format_exc()
        logger.error(f"回测任务 #{task_id} 失败: {str(e)}\n{error_detail}")
        
        try:
            task = BacktestTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = f"{str(e)}\n\n{error_detail}"
            task.save()
        except Exception as db_error:
            logger.error(f"无法更新失败任务状态: {str(db_error)}")
        
        return {
            'success': False,
            'task_id': task_id,
            'error': str(e)
        }
    finally:
        # 取消超时信号
        signal.alarm(0)
        # 关闭数据库连接，避免连接泄漏
        connections.close_all()


def backtest_done_callback(future):
    """
    回测任务完成后的回调函数
    
    Args:
        future: Future对象，包含任务执行结果
    """
    try:
        result = future.result()
        task_id = result.get('task_id')
        
        # 从活动任务列表中移除
        if task_id in active_tasks:
            task_info = active_tasks[task_id]
            # 记录任务执行时间
            duration = time.time() - task_info['started_at']
            logger.info(f"任务 #{task_id} 总执行时间: {duration:.2f}秒")
            del active_tasks[task_id]
            
    except Exception as e:
        logger.error(f"处理回测完成回调时出错: {str(e)}")


def submit_backtest(task_id):
    """
    提交回测任务到进程池执行
    
    Args:
        task_id: 回测任务的ID
    
    Returns:
        bool: 是否成功提交任务
    """
    # 检查任务是否已在运行
    if task_id in active_tasks:
        logger.warning(f"任务 #{task_id} 已经在运行中")
        return False
    
    try:
        # 获取任务确保存在
        task = BacktestTask.objects.get(id=task_id)
        
        # 提交任务到进程池
        future = executor.submit(run_backtest_worker, task_id)
        
        # 添加完成回调
        future.add_done_callback(backtest_done_callback)
        
        # 记录到活动任务字典
        active_tasks[task_id] = {
            'future': future,
            'started_at': time.time(),
            'task': task
        }
        
        logger.info(f"任务 #{task_id} 已提交到进程池")
        return True
        
    except BacktestTask.DoesNotExist:
        logger.error(f"任务 #{task_id} 不存在")
        return False
    except Exception as e:
        logger.error(f"提交任务 #{task_id} 时出错: {str(e)}")
        return False


def cancel_backtest(task_id):
    """
    取消正在运行的回测任务
    
    Args:
        task_id: 回测任务的ID
    
    Returns:
        bool: 是否成功取消任务
    """
    if task_id not in active_tasks:
        logger.warning(f"任务 #{task_id} 不在运行中，无法取消")
        return False
    
    task_info = active_tasks[task_id]
    future = task_info['future']
    
    # 尝试取消任务
    cancelled = future.cancel()
    
    if cancelled:
        logger.info(f"任务 #{task_id} 已成功取消")
        
        # 更新任务状态
        try:
            task = BacktestTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = "任务已被用户取消"
            task.save()
        except Exception as e:
            logger.error(f"更新已取消任务状态失败: {str(e)}")
        
        # 从活动任务列表中移除
        del active_tasks[task_id]
    else:
        logger.warning(f"任务 #{task_id} 无法取消，可能已在执行中")
    
    return cancelled


def get_task_status(task_id):
    """
    获取任务的运行状态
    
    Args:
        task_id: 回测任务的ID
    
    Returns:
        dict: 包含任务运行状态信息的字典
    """
    try:
        task = BacktestTask.objects.get(id=task_id)
        
        status_info = {
            'status': task.status,
            'is_running': task_id in active_tasks,
            'error': task.error_message if task.status == 'failed' else None
        }
        
        # 如果任务正在运行，添加运行时间信息
        if task_id in active_tasks:
            task_info = active_tasks[task_id]
            status_info['runtime'] = time.time() - task_info['started_at']
            
        return status_info
        
    except BacktestTask.DoesNotExist:
        return {
            'status': 'not_found',
            'is_running': False,
            'error': '任务不存在'
        }


def recover_pending_tasks():
    """
    恢复所有处于pending状态的任务
    在服务器重启后调用，确保所有未完成的任务都能继续执行
    """
    pending_tasks = BacktestTask.objects.filter(status='pending')
    for task in pending_tasks:
        logger.info(f"恢复执行任务 #{task.id}")
        submit_backtest(task.id)


def shutdown():
    """
    关闭任务管理器
    在应用关闭时调用，确保所有任务都能正确结束
    """
    logger.info("正在关闭回测任务管理器...")
    
    # 取消所有运行中的任务
    for task_id in list(active_tasks.keys()):
        cancel_backtest(task_id)
    
    # 关闭进程池
    executor.shutdown(wait=True)
    logger.info("回测任务管理器已关闭")
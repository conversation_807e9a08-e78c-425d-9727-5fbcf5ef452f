"""

import backtrader as bt
from backtrader.datas.influxdb_feed import InfluxDBData

cerebro = bt.Cerebro()

# 添加数据
data = InfluxDBData(
    dataname='AAPL',  # 股票代码
    host='localhost',
    port=8086,
    token='your-token',
    org='your-org',
    bucket='stock-data',
    measurement='stock_prices',
    fromdate=datetime.datetime(2020, 1, 1),
    todate=datetime.datetime(2021, 1, 1)
)

cerebro.adddata(data)
cerebro.run()

"""

from __future__ import absolute_import, division, print_function, unicode_literals

import datetime
import collections
from backtrader import date2num
from backtrader.feed import DataBase
from influxdb_client import InfluxDBClient
import pandas as pd


class InfluxDBData(DataBase):
    """
    InfluxDB Data Feed for Backtrader
    """

    params = (
        ("host", "localhost"),
        ("port", 8086),
        ("token", None),
        ("org", None),
        ("bucket", None),
        ("measurement", None),
        ("symbol_field", "symbol"),
        ("datetime_field", "_time"),
        ("open_field", "open"),
        ("high_field", "high"),
        ("low_field", "low"),
        ("close_field", "close"),
        ("volume_field", "volume"),
        ("cache_size", 1000),  # 缓存大小
        ("fromdate", None),
        ("todate", None),
    )

    def __init__(self):
        super(InfluxDBData, self).__init__()
        self.client = None
        self.query_api = None
        self.cache = collections.OrderedDict()  # 使用 OrderedDict 作为 LRU 缓存
        self.current_data = None
        self.current_index = 0
        self.start_date = None
        self.end_date = None

    def start(self):
        super(InfluxDBData, self).start()
        # 初始化 InfluxDB 客户端
        self.client = InfluxDBClient(
            url=f"http://{self.p.host}:{self.p.port}",
            token=self.p.token,
            org=self.p.org,
        )
        self.query_api = self.client.query_api()

        # 设置时间范围
        self.start_date = self.p.fromdate if self.p.fromdate else datetime.datetime.now() - datetime.timedelta(days=30)
        self.end_date = self.p.todate if self.p.todate else datetime.datetime.now()

        # 加载初始数据
        self._load_data()

    def _load_data(self):
        """加载数据到缓存"""
        query = f'''
        from(bucket: "{self.p.bucket}")
          |> range(start: -365d)
          |> filter(fn: (r) => r["_measurement"] == "{self.p.measurement}")
          |> filter(fn: (r) => r["{self.p.symbol_field}"] == "{self.p.dataname}")
          |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
          |> sort(columns: ["_time"])
        '''

        try:
            result = self.query_api.query_data_frame(query)
            
            if result.empty:
                print(f"警告: 未找到数据 (symbol: {self.p.dataname})")
                return

            # 确保时间列是日期时间类型
            result['_time'] = pd.to_datetime(result['_time'])
            
            # 将数据存储到缓存中
            self.cache = result
            self.current_data = result
            self.current_index = 0
            
            print(f"成功加载数据: {len(result)} 条记录")
            print("数据范围:", result['_time'].min(), "至", result['_time'].max())
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
            print(f"查询语句: {query}")

    def _load(self):
        """加载下一个数据点"""
        if self.current_data is None or self.current_index >= len(self.current_data):
            return False

        try:
            # 获取当前数据点
            row = self.current_data.iloc[self.current_index]

            # 设置数据
            self.lines.datetime[0] = date2num(row['_time'].to_pydatetime())
            self.lines.open[0] = float(row[self.p.open_field])
            self.lines.high[0] = float(row[self.p.high_field])
            self.lines.low[0] = float(row[self.p.low_field])
            self.lines.close[0] = float(row[self.p.close_field])
            self.lines.volume[0] = float(row[self.p.volume_field])
            self.lines.openinterest[0] = 0

            self.current_index += 1
            return True
            
        except Exception as e:
            print(f"处理数据点时出错: {e}")
            return False

    def haslivedata(self):
        return False

    def islive(self):
        return False

    def _getline(self, line, ago=0):
        """获取指定行的数据"""
        if ago < 0:
            return None
        
        if self.current_data is None or self.current_index < ago:
            return None
            
        try:
            row = self.current_data.iloc[self.current_index - ago - 1]
            if line == 0:  # datetime
                return date2num(row['_time'].to_pydatetime())
            elif line == 1:  # open
                return float(row[self.p.open_field])
            elif line == 2:  # high
                return float(row[self.p.high_field])
            elif line == 3:  # low
                return float(row[self.p.low_field])
            elif line == 4:  # close
                return float(row[self.p.close_field])
            elif line == 5:  # volume
                return float(row[self.p.volume_field])
            elif line == 6:  # openinterest
                return 0
        except Exception as e:
            print(f"获取数据行时出错: {e}")
            return None
            
        return None

    def stop(self):
        """清理资源"""
        if self.client:
            self.client.close()
        super(InfluxDBData, self).stop()

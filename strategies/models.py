from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from indicators.models import Indicator

User = get_user_model()


class StrategyType(models.TextChoices):
    """策略类型枚举"""
    INDICATOR_BASED = 'INDICATOR', '基于指标'
    CODE_BASED = 'CODE', '代码策略'


class ConditionOperator(models.TextChoices):
    """条件操作符枚举"""
    GREATER_THAN = 'GT', '大于'
    LESS_THAN = 'LT', '小于'
    EQUAL = 'EQ', '等于'
    GREATER_EQUAL = 'GTE', '大于等于'
    LESS_EQUAL = 'LTE', '小于等于'
    BETWEEN = 'BETWEEN', '介于'
    CROSS_ABOVE = 'CROSS_ABOVE', '向上穿越'
    CROSS_BELOW = 'CROSS_BELOW', '向下穿越'


class LogicalOperator(models.TextChoices):
    """逻辑操作符枚举"""
    AND = 'AND', '且'
    OR = 'OR', '或'


class Strategy(models.Model):
    """策略基础模型"""
    name = models.CharField(max_length=100, verbose_name="策略名称")
    description = models.TextField(blank=True, verbose_name="策略描述")
    strategy_type = models.CharField(
        max_length=10,
        choices=StrategyType.choices,
        verbose_name="策略类型"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='strategies',
        verbose_name="创建用户"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "策略"
        verbose_name_plural = "策略"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_strategy_type_display()})"


class CodeStrategy(models.Model):
    """代码策略"""
    strategy = models.OneToOneField(
        Strategy,
        on_delete=models.CASCADE,
        related_name='code_implementation',
        verbose_name="关联策略"
    )
    source_code = models.TextField(verbose_name="策略源代码")
    parameters = models.JSONField(
        default=dict,
        verbose_name="策略参数",
        help_text="格式：{'参数名': {'type': '类型', 'default': '默认值', 'description': '描述'}}"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "代码策略"
        verbose_name_plural = "代码策略"

    def __str__(self):
        return f"代码策略: {self.strategy.name}"


class IndicatorStrategy(models.Model):
    """指标策略"""
    strategy = models.OneToOneField(
        Strategy,
        on_delete=models.CASCADE,
        related_name='indicator_implementation',
        verbose_name="关联策略"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "指标策略"
        verbose_name_plural = "指标策略"

    def __str__(self):
        return f"指标策略: {self.strategy.name}"


class StrategyConditionGroup(models.Model):
    """策略条件组"""
    indicator_strategy = models.ForeignKey(
        IndicatorStrategy,
        on_delete=models.CASCADE,
        related_name='condition_groups',
        verbose_name="指标策略"
    )
    name = models.CharField(max_length=100, verbose_name="条件组名称")
    is_entry = models.BooleanField(verbose_name="是否为开仓条件", help_text="True为开仓条件，False为平仓条件")
    logical_operator = models.CharField(
        max_length=5,
        choices=LogicalOperator.choices,
        default=LogicalOperator.AND,
        verbose_name="组内逻辑关系"
    )
    order = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name="排序"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "策略条件组"
        verbose_name_plural = "策略条件组"
        ordering = ['order']

    def __str__(self):
        condition_type = "开仓" if self.is_entry else "平仓"
        return f"{self.indicator_strategy.strategy.name} - {condition_type}条件组: {self.name}"


class StrategyCondition(models.Model):
    """策略条件"""
    condition_group = models.ForeignKey(
        StrategyConditionGroup,
        on_delete=models.CASCADE,
        related_name='conditions',
        verbose_name="条件组"
    )
    indicator = models.ForeignKey(
        Indicator,
        on_delete=models.CASCADE,
        verbose_name="指标"
    )
    indicator_parameters = models.JSONField(
        default=dict,
        verbose_name="指标参数",
        help_text="指标的参数配置"
    )
    operator = models.CharField(
        max_length=15,
        choices=ConditionOperator.choices,
        verbose_name="比较操作符"
    )
    target_value = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值"
    )
    target_value_min = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值最小值",
        help_text="用于BETWEEN操作符"
    )
    target_value_max = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值最大值",
        help_text="用于BETWEEN操作符"
    )
    compare_indicator = models.ForeignKey(
        Indicator,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='compare_conditions',
        verbose_name="比较指标",
        help_text="用于指标间比较，如穿越等"
    )
    compare_indicator_parameters = models.JSONField(
        default=dict,
        verbose_name="比较指标参数",
        help_text="比较指标的参数配置"
    )
    order = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name="排序"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "策略条件"
        verbose_name_plural = "策略条件"
        ordering = ['order']

    def __str__(self):
        return f"{self.condition_group} - {self.indicator.display_name} {self.get_operator_display()}"


class StrategyBacktest(models.Model):
    """策略回测记录"""
    strategy = models.ForeignKey(
        Strategy,
        on_delete=models.CASCADE,
        related_name='backtests',
        verbose_name="策略"
    )
    name = models.CharField(max_length=100, verbose_name="回测名称")
    symbols = models.JSONField(verbose_name="交易标的", help_text="交易标的列表")
    start_date = models.DateTimeField(verbose_name="开始时间")
    end_date = models.DateTimeField(verbose_name="结束时间")
    timeframe = models.CharField(max_length=20, verbose_name="时间周期")
    initial_cash = models.FloatField(
        default=100000.0,
        validators=[MinValueValidator(1000.0)],
        verbose_name="初始资金"
    )
    commission = models.FloatField(
        default=0.001,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="手续费率"
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '等待中'),
            ('running', '运行中'),
            ('completed', '已完成'),
            ('failed', '失败'),
        ],
        default='pending',
        verbose_name="状态"
    )
    result = models.JSONField(null=True, blank=True, verbose_name="回测结果")
    error_message = models.TextField(null=True, blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "策略回测"
        verbose_name_plural = "策略回测"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.strategy.name} - {self.name}"

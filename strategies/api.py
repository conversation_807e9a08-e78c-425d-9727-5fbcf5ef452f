from ninja_extra import api_controller, route
from typing import List, Optional
from django.db.models import Q, Prefetch
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from pydantic import BaseModel
from .models import (
    Strategy, CodeStrategy, IndicatorStrategy, 
    StrategyConditionGroup, StrategyCondition, StrategyBacktest,
    StrategyType, ConditionOperator, LogicalOperator
)
from indicators.models import Indicator

User = get_user_model()


# Pydantic schemas for request/response
class StrategyConditionSchema(BaseModel):
    indicator_id: int
    indicator_parameters: dict = {}
    operator: str
    target_value: Optional[float] = None
    target_value_min: Optional[float] = None
    target_value_max: Optional[float] = None
    compare_indicator_id: Optional[int] = None
    compare_indicator_parameters: dict = {}
    order: int = 0


class StrategyConditionGroupSchema(BaseModel):
    name: str
    is_entry: bool
    logical_operator: str = "AND"
    conditions: List[StrategyConditionSchema]
    order: int = 0


class CreateIndicatorStrategySchema(BaseModel):
    name: str
    description: str = ""
    condition_groups: List[StrategyConditionGroupSchema]


class CreateCodeStrategySchema(BaseModel):
    name: str
    description: str = ""
    source_code: str
    parameters: dict = {}


class CreateBacktestSchema(BaseModel):
    strategy_id: int
    name: str
    symbols: List[str]
    start_date: str
    end_date: str
    timeframe: str
    initial_cash: float = 100000.0
    commission: float = 0.001


@api_controller("/strategies", tags=["strategies"])
class StrategyController:
    
    @route.get("/strategies", response=List[dict])
    def list_strategies(
        self, 
        request,
        strategy_type: Optional[str] = None,
        search: Optional[str] = None,
        user_id: Optional[int] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0
    ):
        """获取策略列表"""
        queryset = Strategy.objects.select_related('user').prefetch_related(
            'code_implementation',
            'indicator_implementation__condition_groups__conditions__indicator'
        )
        
        # 按类型筛选
        if strategy_type:
            queryset = queryset.filter(strategy_type=strategy_type)
        
        # 按用户筛选
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 排序
        queryset = queryset.order_by('-created_at')
        
        # 分页
        if limit:
            queryset = queryset[offset:offset + limit]
        
        result = []
        for strategy in queryset:
            strategy_data = {
                "id": strategy.id,
                "name": strategy.name,
                "description": strategy.description,
                "strategy_type": strategy.strategy_type,
                "strategy_type_display": strategy.get_strategy_type_display(),
                "user": {
                    "id": strategy.user.id,
                    "username": strategy.user.username
                },
                "is_active": strategy.is_active,
                "created_at": strategy.created_at,
                "updated_at": strategy.updated_at
            }
            
            # 添加策略实现详情
            if strategy.strategy_type == StrategyType.CODE_BASED:
                if hasattr(strategy, 'code_implementation'):
                    strategy_data["code_implementation"] = {
                        "source_code": strategy.code_implementation.source_code,
                        "parameters": strategy.code_implementation.parameters
                    }
            elif strategy.strategy_type == StrategyType.INDICATOR_BASED:
                if hasattr(strategy, 'indicator_implementation'):
                    condition_groups = []
                    for group in strategy.indicator_implementation.condition_groups.all():
                        conditions = []
                        for condition in group.conditions.all():
                            conditions.append({
                                "id": condition.id,
                                "indicator": {
                                    "id": condition.indicator.id,
                                    "display_name": condition.indicator.display_name
                                },
                                "indicator_parameters": condition.indicator_parameters,
                                "operator": condition.operator,
                                "operator_display": condition.get_operator_display(),
                                "target_value": condition.target_value,
                                "target_value_min": condition.target_value_min,
                                "target_value_max": condition.target_value_max,
                                "compare_indicator": {
                                    "id": condition.compare_indicator.id,
                                    "display_name": condition.compare_indicator.display_name
                                } if condition.compare_indicator else None,
                                "compare_indicator_parameters": condition.compare_indicator_parameters,
                                "order": condition.order
                            })
                        
                        condition_groups.append({
                            "id": group.id,
                            "name": group.name,
                            "is_entry": group.is_entry,
                            "logical_operator": group.logical_operator,
                            "logical_operator_display": group.get_logical_operator_display(),
                            "conditions": conditions,
                            "order": group.order
                        })
                    
                    strategy_data["indicator_implementation"] = {
                        "condition_groups": condition_groups
                    }
            
            result.append(strategy_data)
        
        return result
    
    @route.get("/strategies/{strategy_id}", response=dict)
    def get_strategy(self, request, strategy_id: int):
        """获取单个策略详情"""
        try:
            strategy = Strategy.objects.select_related('user').prefetch_related(
                'code_implementation',
                'indicator_implementation__condition_groups__conditions__indicator',
                'indicator_implementation__condition_groups__conditions__compare_indicator'
            ).get(id=strategy_id)
            
            strategy_data = {
                "id": strategy.id,
                "name": strategy.name,
                "description": strategy.description,
                "strategy_type": strategy.strategy_type,
                "strategy_type_display": strategy.get_strategy_type_display(),
                "user": {
                    "id": strategy.user.id,
                    "username": strategy.user.username
                },
                "is_active": strategy.is_active,
                "created_at": strategy.created_at,
                "updated_at": strategy.updated_at
            }
            
            # 添加策略实现详情
            if strategy.strategy_type == StrategyType.CODE_BASED:
                if hasattr(strategy, 'code_implementation'):
                    strategy_data["code_implementation"] = {
                        "source_code": strategy.code_implementation.source_code,
                        "parameters": strategy.code_implementation.parameters
                    }
            elif strategy.strategy_type == StrategyType.INDICATOR_BASED:
                if hasattr(strategy, 'indicator_implementation'):
                    condition_groups = []
                    for group in strategy.indicator_implementation.condition_groups.all():
                        conditions = []
                        for condition in group.conditions.all():
                            conditions.append({
                                "id": condition.id,
                                "indicator": {
                                    "id": condition.indicator.id,
                                    "display_name": condition.indicator.display_name,
                                    "class_name": condition.indicator.class_name
                                },
                                "indicator_parameters": condition.indicator_parameters,
                                "operator": condition.operator,
                                "operator_display": condition.get_operator_display(),
                                "target_value": condition.target_value,
                                "target_value_min": condition.target_value_min,
                                "target_value_max": condition.target_value_max,
                                "compare_indicator": {
                                    "id": condition.compare_indicator.id,
                                    "display_name": condition.compare_indicator.display_name,
                                    "class_name": condition.compare_indicator.class_name
                                } if condition.compare_indicator else None,
                                "compare_indicator_parameters": condition.compare_indicator_parameters,
                                "order": condition.order
                            })
                        
                        condition_groups.append({
                            "id": group.id,
                            "name": group.name,
                            "is_entry": group.is_entry,
                            "logical_operator": group.logical_operator,
                            "logical_operator_display": group.get_logical_operator_display(),
                            "conditions": conditions,
                            "order": group.order
                        })
                    
                    strategy_data["indicator_implementation"] = {
                        "condition_groups": condition_groups
                    }
            
            return strategy_data
            
        except Strategy.DoesNotExist:
            return {"error": "策略不存在"}
    
    @route.post("/strategies/indicator", response=dict)
    def create_indicator_strategy(self, request, payload: CreateIndicatorStrategySchema):
        """创建基于指标的策略"""
        try:
            # 创建策略基础记录
            strategy = Strategy.objects.create(
                name=payload.name,
                description=payload.description,
                strategy_type=StrategyType.INDICATOR_BASED,
                user_id=request.user.id if request.user.is_authenticated else 1  # 默认用户
            )
            
            # 创建指标策略实现
            indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)
            
            # 创建条件组和条件
            for group_data in payload.condition_groups:
                condition_group = StrategyConditionGroup.objects.create(
                    indicator_strategy=indicator_strategy,
                    name=group_data.name,
                    is_entry=group_data.is_entry,
                    logical_operator=group_data.logical_operator,
                    order=group_data.order
                )
                
                for condition_data in group_data.conditions:
                    StrategyCondition.objects.create(
                        condition_group=condition_group,
                        indicator_id=condition_data.indicator_id,
                        indicator_parameters=condition_data.indicator_parameters,
                        operator=condition_data.operator,
                        target_value=condition_data.target_value,
                        target_value_min=condition_data.target_value_min,
                        target_value_max=condition_data.target_value_max,
                        compare_indicator_id=condition_data.compare_indicator_id,
                        compare_indicator_parameters=condition_data.compare_indicator_parameters,
                        order=condition_data.order
                    )
            
            return {"success": True, "strategy_id": strategy.id, "message": "指标策略创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.post("/strategies/code", response=dict)
    def create_code_strategy(self, request, payload: CreateCodeStrategySchema):
        """创建代码策略"""
        try:
            # 创建策略基础记录
            strategy = Strategy.objects.create(
                name=payload.name,
                description=payload.description,
                strategy_type=StrategyType.CODE_BASED,
                user_id=request.user.id if request.user.is_authenticated else 1  # 默认用户
            )
            
            # 创建代码策略实现
            CodeStrategy.objects.create(
                strategy=strategy,
                source_code=payload.source_code,
                parameters=payload.parameters
            )
            
            return {"success": True, "strategy_id": strategy.id, "message": "代码策略创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.put("/strategies/{strategy_id}", response=dict)
    def update_strategy(self, request, strategy_id: int, payload: dict):
        """更新策略"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            
            # 更新基础信息
            strategy.name = payload.get('name', strategy.name)
            strategy.description = payload.get('description', strategy.description)
            strategy.is_active = payload.get('is_active', strategy.is_active)
            strategy.save()
            
            return {"success": True, "message": "策略更新成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.delete("/strategies/{strategy_id}", response=dict)
    def delete_strategy(self, request, strategy_id: int):
        """删除策略"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            strategy.delete()
            return {"success": True, "message": "策略删除成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.get("/enums", response=dict)
    def get_enums(self, request):
        """获取枚举值"""
        return {
            "strategy_types": [
                {"value": choice.value, "label": choice.label}
                for choice in StrategyType
            ],
            "condition_operators": [
                {"value": choice.value, "label": choice.label}
                for choice in ConditionOperator
            ],
            "logical_operators": [
                {"value": choice.value, "label": choice.label}
                for choice in LogicalOperator
            ]
        }
    
    @route.post("/strategies/{strategy_id}/backtest", response=dict)
    def create_backtest(self, request, strategy_id: int, payload: CreateBacktestSchema):
        """创建策略回测"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            
            backtest = StrategyBacktest.objects.create(
                strategy=strategy,
                name=payload.name,
                symbols=payload.symbols,
                start_date=payload.start_date,
                end_date=payload.end_date,
                timeframe=payload.timeframe,
                initial_cash=payload.initial_cash,
                commission=payload.commission
            )
            
            return {"success": True, "backtest_id": backtest.id, "message": "回测任务创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.get("/strategies/{strategy_id}/backtests", response=List[dict])
    def get_strategy_backtests(self, request, strategy_id: int):
        """获取策略的回测记录"""
        backtests = StrategyBacktest.objects.filter(strategy_id=strategy_id).order_by('-created_at')
        
        return [
            {
                "id": bt.id,
                "name": bt.name,
                "symbols": bt.symbols,
                "start_date": bt.start_date,
                "end_date": bt.end_date,
                "timeframe": bt.timeframe,
                "initial_cash": bt.initial_cash,
                "commission": bt.commission,
                "status": bt.status,
                "result": bt.result,
                "error_message": bt.error_message,
                "created_at": bt.created_at,
                "updated_at": bt.updated_at
            }
            for bt in backtests
        ] 
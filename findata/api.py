from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal

from django.db.models import Count, Max, Min, Avg, Q
from django.shortcuts import get_object_or_404
from django.http import Http404
from ninja_extra import NinjaExtraAPI, api_controller, http_get
from ninja import Query, Schema

from .models import DataSource, Market, Symbol, DataFrequency, AStockDailyData





# API数据模式定义
class DataSourceSchema(Schema):
    id: int
    name: str
    description: str
    provider: str
    is_active: bool
    created_at: datetime


class MarketSchema(Schema):
    id: int
    code: str
    name: str
    market_type: str
    timezone: str
    trading_hours: Dict[str, Any]
    is_active: bool


class SymbolSchema(Schema):
    id: int
    code: str
    name: str
    market: MarketSchema
    industry: str
    sector: str
    list_date: Optional[date]
    delist_date: Optional[date]
    is_active: bool


class DataFrequencySchema(Schema):
    id: int
    code: str
    name: str
    frequency_type: str
    seconds: int
    is_active: bool


class AStockDailyDataSchema(Schema):
    id: int
    symbol: SymbolSchema
    data_source: DataSourceSchema
    time: datetime
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: int
    amount: Optional[Decimal]
    pre_close: Optional[Decimal]
    change: Optional[Decimal]
    pct_change: Optional[Decimal]


class DataOverviewSchema(Schema):
    """数据概览"""

    total_symbols: int
    total_records: int
    data_sources_count: int
    frequencies_count: int
    markets_count: int
    last_update: Optional[datetime]


class SymbolDataSummarySchema(Schema):
    """标的数据汇总"""

    symbol_code: str
    symbol_name: str
    market_name: str
    data_count: int
    frequencies: List[str]
    data_sources: List[str]
    start_date: Optional[datetime]
    end_date: Optional[datetime]


class AStockDailyDataQuerySchema(Schema):
    """A股日线数据查询参数"""

    symbol_code: Optional[str] = None
    market_code: Optional[str] = None
    data_source_id: Optional[int] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None


# API控制器
@api_controller("/findata", tags=["金融数据管理"])
class FinDataController:

    @http_get("/overview", response=DataOverviewSchema, summary="数据概览")
    def get_data_overview(self):
        """获取数据概览统计"""
        total_symbols = Symbol.objects.filter(is_active=True).count()
        total_records = AStockDailyData.objects.count()
        data_sources_count = DataSource.objects.filter(is_active=True).count()
        frequencies_count = DataFrequency.objects.filter(is_active=True).count()
        markets_count = Market.objects.filter(is_active=True).count()

        # 获取最后更新时间
        last_update = AStockDailyData.objects.aggregate(last_update=Max("updated_at"))[
            "last_update"
        ]

        return {
            "total_symbols": total_symbols,
            "total_records": total_records,
            "data_sources_count": data_sources_count,
            "frequencies_count": frequencies_count,
            "markets_count": markets_count,
            "last_update": last_update,
        }

    @http_get("/data-sources", response=List[DataSourceSchema], summary="数据源列表")
    def list_data_sources(self):
        """获取所有数据源"""
        return DataSource.objects.filter(is_active=True)

    @http_get("/markets", response=List[MarketSchema], summary="市场列表")
    def list_markets(self):
        """获取所有市场"""
        return Market.objects.filter(is_active=True)

    @http_get(
        "/frequencies", response=List[DataFrequencySchema], summary="数据频率列表"
    )
    def list_frequencies(self):
        """获取所有数据频率"""
        return DataFrequency.objects.filter(is_active=True)

    @http_get("/symbols", response=Dict[str, Any], summary="交易标的列表")
    def list_symbols(
        self,
        market_code: Optional[str] = Query(None, description="市场代码"),
        page: int = Query(1, description="页码"),
        page_size: int = Query(50, description="每页数量")
    ):
        """获取交易标的列表"""
        queryset = Symbol.objects.select_related("market").filter(is_active=True)

        if market_code:
            queryset = queryset.filter(market__code=market_code)

        # 手动分页
        total_count = queryset.count()
        offset = (page - 1) * page_size
        items = list(queryset[offset:offset + page_size])

        # 序列化数据
        serialized_items = []
        for symbol in items:
            serialized_items.append({
                "id": symbol.id,
                "code": symbol.code,
                "name": symbol.name,
                "market": {
                    "id": symbol.market.id,
                    "code": symbol.market.code,
                    "name": symbol.market.name,
                    "market_type": symbol.market.market_type,
                    "timezone": symbol.market.timezone,
                    "trading_hours": symbol.market.trading_hours,
                    "is_active": symbol.market.is_active,
                },
                "industry": symbol.industry,
                "sector": symbol.sector,
                "list_date": symbol.list_date,
                "delist_date": symbol.delist_date,
                "is_active": symbol.is_active,
            })

        return {
            "items": serialized_items,
            "count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }

    @http_get(
        "/symbols/summary",
        response=List[SymbolDataSummarySchema],
        summary="标的数据汇总",
    )
    def get_symbols_summary(
        self, market_code: Optional[str] = Query(None, description="市场代码")
    ):
        """获取标的数据汇总信息"""
        from django.db.models import Count, Min, Max

        # 构建查询
        queryset = Symbol.objects.select_related("market").filter(is_active=True)

        if market_code:
            queryset = queryset.filter(market__code=market_code)

        # 按标的代码排序，确保结果一致性
        queryset = queryset.order_by('code')

        # 限制返回数量，避免超时（如果前端需要特定标的，应该使用市场过滤）
        queryset = queryset[:200]

        # 获取每个标的的数据统计 - 使用聚合查询优化性能
        symbols_data = []
        for symbol in queryset:
            # 使用聚合查询一次性获取统计信息
            stats = AStockDailyData.objects.filter(symbol=symbol).aggregate(
                data_count=Count('id'),
                start_date=Min('time'),
                end_date=Max('time')
            )

            # 获取数据源信息（去重）- 使用Python set确保去重
            data_sources_queryset = (
                AStockDailyData.objects.filter(symbol=symbol)
                .values_list("data_source__name", flat=True)
            )
            data_sources = list(set(data_sources_queryset))

            symbols_data.append({
                "symbol_code": symbol.code,
                "symbol_name": symbol.name,
                "market_name": symbol.market.name,
                "data_count": stats['data_count'] or 0,
                "frequencies": ["日线"],  # 固定为日线，因为目前只有A股日线数据
                "data_sources": data_sources,
                "start_date": stats['start_date'],
                "end_date": stats['end_date'],
            })

        return symbols_data

    @http_get("/symbols/{symbol_id}", response=SymbolSchema, summary="获取单个标的信息")
    def get_symbol(self, symbol_id: int):
        """获取单个交易标的详情"""
        return get_object_or_404(Symbol, id=symbol_id, is_active=True)

    @http_get(
        "/a-stock-daily-data",
        response=Dict[str, Any],
        summary="A股日线数据查询",
    )
    def list_a_stock_daily_data(
        self,
        filters: AStockDailyDataQuerySchema = Query(...),
        page: int = Query(1, description="页码"),
        page_size: int = Query(100, description="每页数量")
    ):
        """查询A股日线数据"""
        queryset = AStockDailyData.objects.select_related(
            "symbol", "symbol__market", "data_source"
        ).all()

        # 应用过滤条件
        if filters.symbol_code:
            queryset = queryset.filter(symbol__code=filters.symbol_code)

        if filters.market_code:
            queryset = queryset.filter(symbol__market__code=filters.market_code)

        if filters.data_source_id:
            queryset = queryset.filter(data_source_id=filters.data_source_id)

        if filters.start_date:
            queryset = queryset.filter(time__date__gte=filters.start_date)

        if filters.end_date:
            queryset = queryset.filter(time__date__lte=filters.end_date)

        queryset = queryset.order_by("-time")

        # 手动分页
        total_count = queryset.count()
        offset = (page - 1) * page_size
        items = list(queryset[offset:offset + page_size])

        # 序列化数据
        serialized_items = []
        for data in items:
            serialized_items.append({
                "id": data.id,
                "symbol": {
                    "id": data.symbol.id,
                    "code": data.symbol.code,
                    "name": data.symbol.name,
                    "market": {
                        "id": data.symbol.market.id,
                        "code": data.symbol.market.code,
                        "name": data.symbol.market.name,
                        "market_type": data.symbol.market.market_type,
                        "timezone": data.symbol.market.timezone,
                        "trading_hours": data.symbol.market.trading_hours,
                        "is_active": data.symbol.market.is_active,
                    },
                    "industry": data.symbol.industry,
                    "sector": data.symbol.sector,
                    "list_date": data.symbol.list_date,
                    "delist_date": data.symbol.delist_date,
                    "is_active": data.symbol.is_active,
                },
                "data_source": {
                    "id": data.data_source.id,
                    "name": data.data_source.name,
                    "description": data.data_source.description,
                    "provider": data.data_source.provider,
                    "is_active": data.data_source.is_active,
                    "created_at": data.data_source.created_at,
                },
                "time": data.time,
                "open_price": float(data.open_price),
                "high_price": float(data.high_price),
                "low_price": float(data.low_price),
                "close_price": float(data.close_price),
                "volume": data.volume,
                "amount": float(data.amount) if data.amount else None,
                "pre_close": float(data.pre_close) if data.pre_close else None,
                "change": float(data.change) if data.change else None,
                "pct_change": float(data.pct_change) if data.pct_change else None,
            })

        return {
            "items": serialized_items,
            "count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }

    @http_get("/data-quality", summary="数据质量报告")
    def get_data_quality_report(self):
        """获取数据质量报告"""
        # 按市场统计数据质量
        market_quality = []
        for market in Market.objects.filter(is_active=True):
            symbols_count = Symbol.objects.filter(market=market, is_active=True).count()
            data_count = AStockDailyData.objects.filter(symbol__market=market).count()

            market_quality.append(
                {
                    "market_code": market.code,
                    "market_name": market.name,
                    "symbols_count": symbols_count,
                    "data_records": data_count,
                    "avg_quality_score": 100.0,  # 暂时固定为100
                }
            )

        return {
            "market_quality": market_quality,
            "total_records": AStockDailyData.objects.count(),
            "total_symbols": Symbol.objects.filter(is_active=True).count(),
        }

    @http_get("/latest-data/{symbol_code}", summary="获取标的最新数据")
    def get_latest_data(self, symbol_code: str):
        """获取指定标的的最新数据"""
        try:
            symbol = Symbol.objects.get(code=symbol_code, is_active=True)
        except Symbol.DoesNotExist:
            raise Http404("标的不存在")

        queryset = AStockDailyData.objects.filter(symbol=symbol).select_related(
            "data_source"
        )

        latest_data = queryset.order_by("-time").first()

        if not latest_data:
            return {"message": "暂无数据"}

        return {
            "symbol_code": symbol.code,
            "symbol_name": symbol.name,
            "frequency": "日线",
            "data_source": latest_data.data_source.name,
            "time": latest_data.time,
            "open_price": latest_data.open_price,
            "high_price": latest_data.high_price,
            "low_price": latest_data.low_price,
            "close_price": latest_data.close_price,
            "volume": latest_data.volume,
            "change": latest_data.change,
            "pct_change": latest_data.pct_change,
        }

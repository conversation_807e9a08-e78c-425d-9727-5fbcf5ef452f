from ninja_extra import api_controller, route
from typing import List, Optional
from django.db.models import Q
from .models import Indicator, IndicatorSource, IndicatorCategory


@api_controller("/indicators", tags=["indicators"])
class IndicatorController:
    @route.get("/indicators", response=List[dict])
    def list_indicators(
        self, 
        request,
        source: Optional[str] = None,
        category: Optional[str] = None,
        search: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0
    ):
        """获取指标列表"""
        queryset = Indicator.objects.all()
        
        # 按来源筛选
        if source:
            queryset = queryset.filter(source=source)
        
        # 按分类筛选
        if category:
            queryset = queryset.filter(category=category)
        
        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(class_name__icontains=search) |
                Q(display_name__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 排序
        queryset = queryset.order_by('category', 'class_name')
        
        # 分页
        if limit:
            queryset = queryset[offset:offset + limit]
        
        return [
            {
                "id": indicator.id,
                "class_name": indicator.class_name,
                "display_name": indicator.display_name,
                "source": indicator.source,
                "source_display": indicator.get_source_display(),
                "category": indicator.category,
                "category_display": indicator.get_category_display(),
                "description": indicator.description,
                "required_lines": indicator.required_lines,
                "parameters": indicator.parameters,
                "created_at": indicator.created_at,
                "updated_at": indicator.updated_at
            }
            for indicator in queryset
        ]
    
    @route.get("/indicators/{indicator_id}", response=dict)
    def get_indicator(self, request, indicator_id: int):
        """获取单个指标详情"""
        try:
            indicator = Indicator.objects.get(id=indicator_id)
            return {
                "id": indicator.id,
                "class_name": indicator.class_name,
                "display_name": indicator.display_name,
                "source": indicator.source,
                "source_display": indicator.get_source_display(),
                "category": indicator.category,
                "category_display": indicator.get_category_display(),
                "description": indicator.description,
                "required_lines": indicator.required_lines,
                "parameters": indicator.parameters,
                "created_at": indicator.created_at,
                "updated_at": indicator.updated_at
            }
        except Indicator.DoesNotExist:
            return {"error": "指标不存在"}
    
    @route.get("/categories", response=List[dict])
    def list_categories(self, request):
        """获取所有指标分类"""
        return [
            {
                "value": category.value,
                "label": category.label,
                "count": Indicator.objects.filter(category=category.value).count()
            }
            for category in IndicatorCategory
        ]
    
    @route.get("/sources", response=List[dict])
    def list_sources(self, request):
        """获取所有指标来源"""
        return [
            {
                "value": source.value,
                "label": source.label,
                "count": Indicator.objects.filter(source=source.value).count()
            }
            for source in IndicatorSource
        ]

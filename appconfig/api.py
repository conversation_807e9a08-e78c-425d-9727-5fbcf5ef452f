from ninja_extra import NinjaExtraAPI
from btwrapper.api import BacktestController
from indicators.api import IndicatorController
from strategies.api import StrategyController
from findata.api import FinDataController

# 创建一个 NinjaExtraAPI 实例
api = NinjaExtraAPI()
api.register_controllers(IndicatorController)
api.register_controllers(BacktestController)
api.register_controllers(StrategyController)
api.register_controllers(FinDataController)
